var express = require("express");
var router = express.Router();
const fs = require("fs");
const path = require("path");
const readline = require("readline");
const { performance } = require("perf_hooks");

const { loadAndExtractExcel } = require("../utils/index");
const pdfVal = require("../utils/pdfVal/index");
const { sheetToCsv } = require("../utils/index");

const auth = require("#middlewares/auth");
const Upload = require("#middlewares/upload");

const getConfig = require("../utils/config");

const ReportService = require("../services/reportService");
const clientService = require("../services/clientService");
const  rbac  = require("#src/_middlewares/rbac");

router.post("/", auth,rbac("uploadDepositFile"), Upload, processFile);
router.get("/reports", auth,rbac("getDepositReport"), getReports);


router.delete("/reports/:id", deleteFile);
router.get("/reports/invoices/:fileId", auth, fetchInvoiceReports);
router.post("/checkFileName", checkFileName);

async function processFile(req, res, next) {
  let check = [];
  const t0 = performance.now();
  const headers = "Account_no,Ability" + "\n";

  const fileName = req.fileName;
  const { mambu_env, base_url, sub } = req.mambuData;
  console.log("mambuData", req.mambuData);

  const encodedKey = req.mambuUserID;
  const realFileName = req.realFileName;

  const config = getConfig({ mambu_env, baseUrl: base_url, sub });
  const resultfile =
    fileName.split(".xlsx")[0] + new Date().toISOString() + ".csv";

  try {
    const _fileName = fileName.split(".xlsx")[0];
    console.log("launching work.....");
    // await client.connect();
    // await client.flushAll();

    const { pathToExcel, pathToCsv } = fileLocations(fileName, _fileName);
    await sheetToCsv(pathToExcel, pathToCsv);

    const referenceStream = fs.createReadStream(
      path.join(__dirname, "../../uploads/" + _fileName + ".csv")
    );

    const rl = readline.createInterface({
      input: referenceStream,
    });

    rl.on("line", (line) => {
      line = line.split(",");
      if (line[0].length > 1) {
        let needle = line;
        console.log({
          line,
        });
        check.push(needle);
      }
    });

    rl.on("close", async () => {
      const { email = null } = await new clientService(config).getUser(
        encodedKey
      );

      const storedFile = await ReportService.createFile({
        fileName,
        uploadedByEncodedKey: email ?? encodedKey,
        realFileName,
        sub,
        environment: mambu_env,
        uploadedCount: check.length - 1,
        reportedCount: 0,
      });

      const recordsInserted = await pdfVal.insertRecords({
        records: check.slice(1),
        baseUrl: base_url,
        sub,
        environment: mambu_env,
        userEncodedKey: email ?? encodedKey,
        fileId: storedFile["id"],
        fileName: req.fileName.split("_")[1].split(".xlsx")[0],
        actualName: req.fileName.split("_")[1].split(".xlsx")[0],
      });

      loadAndExtractExcel.deleteFile(fileName, true, false);
      loadAndExtractExcel.deleteFile(_fileName + ".csv", true, false);

      return res.status(200).send({
        message: "file received successfully",
        recordsInserted,
      });
    });
  } catch (error) {
    next(error);
  }
}

function getReports(req, res, next) {
  const { mambu_env, base_url, sub } = req.mambuData;
  ReportService.fetchFiles({
    sub,
    environment: mambu_env,
  })
    .then((resp) => res.json(resp))
    .catch(next);
}

function deleteFile(req, res, next) {
  ReportService.deleteFile(req.params.id)
    .then((resp) => res.json(resp))
    .catch(next);
}

function fetchInvoiceReports(req, res, next) {
  ReportService.fetchInvoiceReports(req.params.fileId)
    .then((resp) => res.json(resp))
    .catch(next);
}

//helpers
function fileLocations(originalFileName, csvFileName) {
  const pathToExcel = path.join(__dirname, "../../uploads/" + originalFileName);
  const pathToCsv = path.join(
    __dirname,
    "../../uploads/" + csvFileName + ".csv"
  );
  return {
    pathToExcel,
    pathToCsv,
  };
}

function checkFileName(req, res, next) {
  const { fileName } = req.body;

  let loweFileName = fileName
    .replace(/[^a-zA-Z0-9]/g, "")
    .toLowerCase()
    .trim();

  ReportService.findSimilarFile(loweFileName)
    .then((resp) => {
      if (resp > 0) {
        throw new Error("file name already exists");
      }

      return res.json(resp);
    })
    .catch(next);
}
module.exports = router;
