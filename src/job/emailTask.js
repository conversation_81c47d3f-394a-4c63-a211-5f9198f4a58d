const schedule = require("node-schedule");
const { generateExcel } = require("../utils/excel");
const {
  generateReportandsendEmail,
} = require("../utils/generateReportandsendEmail.js");

const email = require("../utils/email");

module.exports = (app) => {
  const { Reports } = app.get("models"); // Get the Reports model from the app

  let job = schedule.scheduleJob("*/10 * * * * *", async function () {
    // */10 * * * * *
    // */5 08-23 * * 1
    //  0  8  * * 1
    // 35 10 * * 2
    console.log("Email Runner triggered");

    try {
      const reportData = await Reports.findAll({ where: { status: 1 } });

      if (reportData.length === 0) {
        console.log("No pending reports to process.");
        return;
      }

      console.log(`Found ${reportData.length} reports to process.`);

      // Generate Excel
      const excel = await generateExcel(reportData, "LOAN_STATEMENT");

      const reportIds = reportData.map((report) => report.id);

      // Send email
      await email(
        {
          message: `Dear Team,

          I hope this email finds you well. As per your request, please find attached a report containing the list of all deposit accounts along with their balances as at the end of the month.

          Best regards,
          Deposit Analysis Team`,
          to_who: ["<EMAIL>"],

          // to_who: [
          //   "<EMAIL>",
          //   "<EMAIL>",
          //   "<EMAIL>",
          // ],

        },
        [
          {
            filename: "DEPOSITS_REPORT.xlsx",
            content: excel,
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          },
        ]
      );

      // Update report status to 'sent'
      await Reports.update({ status: 1 }, { where: { id: reportIds } });
      console.log("Reports processed and status updated.");
    } catch (error) {
      console.error("Error in email task:", error);
    }
  });
};
