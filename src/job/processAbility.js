const schedule = require("node-schedule");
const pdfVal = require("../utils/pdfVal");
const ReportService = require("../services/reportService");
const depositreports = require("../controllers/depositreports");
const emailTask = require("./emailTask");

module.exports = (app) => {
  let rescheduled = false;

  let job = schedule.scheduleJob("*/2 * * * * *", async function () {
    console.log("==Runner triggered==");
    const task = await ReportService.getTask();
    try {
      if (task && task.length > 0) {
        console.log("Processing tasks...");
        let promises = task.map((element) => {
          try {
            depositreports.deposits(element).then((res) => {
              console.log(`Processed task: ${element.id}`);
            });
          } catch (error) {
            console.error(`Error processing task ${element.id}:`, error);
          }
        });

        // await Promise.all(promises); // Wait for all tasks to complete
        // console.log("All tasks processed. Triggering email...");

        // Trigger email task after processing
        // await emailTask(app);
      }
    } catch (error) {
      console.error("Error in cron job execution:", error);
    }
  });
};

// const schedule = require("node-schedule");
// const pdfVal = require("../utils/pdfVal");
// const ReportService = require("../services/reportService");
// const depositreports = require("../controllers/depositreports");

// module.exports = (app) => {
//   let rescheduled = false;
//   let job = schedule.scheduleJob({ second: 5 }, async function () {
//     console.log("==Runner triggered==");

//     const task = await ReportService.getTask();

//     try {
//       const task = await ReportService.getTask();
//       console.log("task", task);

//       if (task && task.length > 0) {
//         job.reschedule({ second: 5 });
//         rescheduled = true;
//       } else if (rescheduled) {
//         console.log("Running rescheduled job...");
//         job.reschedule({ second: 10 });
//         rescheduled = false;
//       }

//       if (task && task.length > 0) {
//         console.log("Processing tasks...");
//         let promises = task.map(async (element) => {
//           try {
//             await depositreports.deposits(element); // Ensure deposits is asynchronous
//             console.log(`Processed task: ${element.id}`);
//           } catch (error) {
//             console.error(`Error processing task ${element.id}:`, error);
//           }
//         });

//         await Promise.all(promises); // Wait for all tasks to complete
//       }
//     } catch (error) {
//       console.error("Error in cron job execution:", error);
//     }
//   });
// };

// const schedule = require("node-schedule");
// const pdfVal = require("../utils/pdfVal");
// const ReportService = require("../services/reportService");

// // const { selectData } = require("../services/db");
// const { generateReportandsendEmail } = require("../utils/generateReportandsendEmail");
// const handleLoans = require("./handleLoans");

// module.exports = (app) => {
//   let job = schedule.scheduleJob("1 12 * * 0", async function () {
//     handleLoans(app)
//   });
// };
