const Busboy = require("busboy");
const fs = require("fs");
const path = require("path");
const { v4: uuidv4 } = require("uuid");

const upload = function (req, res, next) {
  let _filePath;
  let _fileName;
  let _mimeType;
  let trxDetails = {};

  var busboy = Busboy({
    headers: req.headers,
  });

  let formData = new Map();

  busboy.on("field", function (fieldname, val) {
    formData.set(fieldname, val);
  });

  busboy.on("file", function (name, file, info) {
    let { filename, encoding, mimeType } = info;

    console.log(
      `File [${name}]: filename: %j, encoding: %j, mimeType: %j`,
      filename,
      encoding,
      mimeType
    );
    let actualName = filename;
    filename = uuidv4() + "_" + filename;

    var saveTo = path.join(__dirname, "../../uploads/" + filename);
    console.log({
      saveTo,
    });
    _filePath = saveTo;
    _fileName = filename;
    _mimeType = mimeType;
    req.filePath = _filePath;
    req.fileName = _fileName;
    req.realFileName = actualName;
    req.mimeType = _mimeType;
    console.log({ trxDetails });
    file.pipe(fs.createWriteStream(saveTo));
  });

  busboy.on("finish", function () {
    // console.log("busboy finnnnnnnnnnnsded", formData.get("date"));
    // req.date = formData.get("date");

    return next();
  });

  req.pipe(busboy);
};

module.exports = upload;
