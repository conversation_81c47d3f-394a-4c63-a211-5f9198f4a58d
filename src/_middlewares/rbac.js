const { PermissionService } = require("../services/permissions");
const clientService = require("../services/clientService");
const getConfig = require("../utils/config");

/**
 * Role-Based Access Control (RBAC) middleware.
 * Checks if a user has permission to perform a specific action.
 * @param {string} action - The action to authorize.
 * @returns {Function} - Express middleware function.
 */

const rbac = (action) => {
  return async (req, res, next) => {
    // Extract user ID and base URL from request data
    const { userId, base_url } = req.mambuData;
    const { mambu_env = "production", sub = "platinumkenya" } = {
      mambu_env: "production",
      sub: "platinumkenya",
      source: "ui",
    };

    // Retrieve configuration based on environment and subscription
    const config = getConfig({ mambu_env, sub });

    // Initialize services
    const service = new PermissionService({});
    const client = new clientService(config);

    try {
      // Fetch user details from client service
      const user = await client.getUser(userId);
      console.log("user", user);
      console.log("action", action);

      // Check user's permission for the requested action
      const permission = await service.authorize(action, user.username);

      // If authorized, proceed to the next middleware
      if (permission.data && permission.data.authorized) {
        return next();
      } else {
        // If not authorized, return a 403 Forbidden response
        return res.status(403).json({
          message:
            (permission.response && permission.response.data.message) ||
            "You do not have permission to perform this action",
        });
      }
    } catch (error) {
      console.log("error", error);

      // Handle errors and return a 403 Forbidden response
      return res.status(403).json({
        message:
          (error.response && error.response.data.message) ||
          "You do not have permission to perform this action",
      });
    }
  };
};

module.exports = rbac;
