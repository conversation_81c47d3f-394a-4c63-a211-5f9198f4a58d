'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Files extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.hasMany(models.Reports, {
        foreignKey: "fileId",
        as: "reports",
      });
    }
  }
  Files.init(
    {
      fileName: {
        type: DataTypes.STRING,
        unique: true,
      },
      realFileName: {
        type: DataTypes.STRING,
        unique: true,
      },
      uploadedByEncodedKey: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      // email: {
      //   type: DataTypes.STRING,
      //   allowNull: false
      // },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: "fileReceived",
      },
      environment: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      sub: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      uploadedCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      reportedCount: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      modelName: "Files",
      tableName: "Files",
    }
  );
  return Files;
};