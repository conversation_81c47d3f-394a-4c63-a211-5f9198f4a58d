'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Reports extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.belongsTo(models.Files, {
        foreignKey: "fileId",
        as: "file",
      });
    }
  }
  Reports.init({
    fileName: DataTypes.STRING,
    accountId: DataTypes.STRING,
    depositName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    clientName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    balance: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    LatestdepositDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    environment: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    sub: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    processed: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: "1:sent,2:failed,0:pending"
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
    {
      sequelize,
      modelName: "Reports",
      tableName: "Reports",
    });
  return Reports;
};




