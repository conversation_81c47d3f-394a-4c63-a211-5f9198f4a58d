const express = require('express');
const morgan = require('morgan');
const cors = require('cors');
const session = require("./utils/session.js");
require('dotenv').config();
const path = require("path");
const readSession = require('./utils/readSession');
const apiRoutes = require("./routes");

const model = require('./db/models/index.js');
const middlewares = require('./middleware.js');

const fronteend = path.join(__dirname, "../public/dist");
const template = path.join(__dirname, "../public/template.xlsx");
const app = express();

// Attach models to app
app.set("models", model);

require("./job/processAbility")(app);
// require("./job/emailTask.js")(app);

process.env.TZ = 'Africa/Nairobi';

app.use(morgan("dev"));
app.use(cors());
app.use(express.urlencoded({ extended: false }));
app.use(express.static("public"));
app.use(express.json());
app.use(express.static(fronteend));

const sessions = {}

app.get("/deposits/app-definition/", async (req, res) => {
  res.status(200).sendFile(path.join(__dirname, "../public/dev.xml"));
});

app.get("/deposits/template", async (req, res) => {
  console.log("Template route accessed (no trailing slash)");
  console.log("Template path:", template);
  res.status(200).sendFile(template);
});

app.get("/deposits/template/", async (req, res) => {
  console.log("Template route accessed (with trailing slash)");
  console.log("Template path:", template);
  res.status(200).sendFile(template);
});

app.get("/", (req, res) => {
  res.json({
    message: "🦄🌈✨👋🌎🌍🌏✨🌈🦄",
  });
});

app.post("/deposits/ui/", (req, res, next) => {
  console.log(req.body);
  const _signed = req.body.signed_request;
  session(_signed);
  req.isrequestvalid = true
  const signedpart1 = _signed.split(".")[0]
  req.signedToken = signedpart1
  req.method = "GET";
  next();
});


app.use('/deposits/ui',
  function (req, res, next) {
    if (req.isrequestvalid) {
      let expiry = new Date().getTime();
      expiry += 1000 * 2;
      sessions[req.signedToken] = { expiresIn: expiry };
    }
    const mambuUser = JSON.parse(readSession() || JSON.stringify({ session: '' })).session;
    const sess = sessions[mambuUser.split('.')[0]];
    console.log("sess",sess)
    const mambuUserSession = req.header('mambuUser');
    console.log("mambuUserSession",mambuUserSession)
    if (sess || mambuUserSession) {
      if (sess && sess.expiresIn > new Date().getTime() || mambuUserSession) {
        next();
      } else {
        // Send the custom HTML page for unauthorized access
        return res.status(404).sendFile(path.join(__dirname, "../public/dist/error404.html"));
      }
    } else {
      // Send the custom HTML page for unauthorized access
      return res.status(404).sendFile(path.join(__dirname, "../public/dist/error404.html"));
    }
  },

  express.static(path.join(__dirname, "../public/dist"))
);


model.sequelize.sync().then(() => {
  console.log("Database Synced");
});
app.use("/deposits/v1", apiRoutes);


app.use(middlewares.notFound);
app.use(middlewares.errorHandler);

module.exports = app;
