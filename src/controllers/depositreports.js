const ClientService = require("../services/clientService");
const ReportService = require("../services/reportService");

const getConfig = require("../utils/config");
const extractMambuUser = require("../utils/extractMambuUser");

const controller = {};

controller.deposits = async function (account) {
  const { mambu_env = "production", sub = "platinumkenya" } = {
    mambu_env: "production",
    sub: "platinumkenya",
    source: "ui",
  };

  const config = getConfig({
    mambu_env,
    sub,
  });

  try {
    console.log("semaaaaaaaaaaaaa")
    const clientService = new ClientService(config);
    console.log("roooooting")
    const transactions = await clientService.getLoanTransaction(account.accountId);
    console.log("hereeer!!!!!!!!!")
    const transaction = transactions.data[0]
    const latestDepositDate = transaction ? transaction.valueDate : "";
    console.log({ latestDepositDate });

    await ReportService.updateReportById(account.id, {
      status:1,
      LatestdepositDate:latestDepositDate
    });
  } catch (error) {
    if(error.response && error.response.data){
      console.log(error.response.data)
    }
  }
};

module.exports = controller;