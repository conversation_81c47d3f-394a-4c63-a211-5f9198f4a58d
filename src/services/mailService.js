const EmailClient = require("../utils/email");
const fs = require("fs");
const path = require("path");


class MailService {
    constructor() { }
    async _sendReportEmail(pathToAttachment) {
        const fileName = "depositReports"
        console.log({
            pathToAttachment,
        });
        const attachment = fs.readFileSync(pathToAttachment).toString("base64");

        try {
            let message =
                "Please find attached reports of Deposit Accounts ";
            let data = {
                "to": [
                    {
                        "email": "<EMAIL>"
                    },


                ],
                subject: `DEPOSIT Reports ${fileName} results`,
                text: message,
                attachments: [
                    {
                        content: attachment,
                        filename: fileName,
                        type: "application/vnd.ms-excel",
                        disposition: "attachment",
                    },
                ],
            };

            const resp = await EmailClient(data);
            return {
                emailSent: true,
                emailData: resp,
            };
        } catch (error) {
            console.log(JSON.stringify(error, null, 3))
            const failedObj = error?.response?.body?.errors[0] || {
                errorReason: error.message ?? "unknown",
                errorSource: "sendgrid",
            };
            return {
                emailSent: false,
                emailData: {
                    errorReason: failedObj.message ?? "unknown",
                    errorSource: failedObj.field ?? "sendgrid",
                },
            };
        }
    }

}

module.exports = new MailService();


