const axios = require("axios").default;
/**
 * PermissionService class handles authorization requests
 * by communicating with the permissions API.
 */
class PermissionService {
  constructor() {
    // Creates an Axios client with the base URL and authorization header
    this.client = axios.create({
      baseURL: process.env.PERMISSIONS_URL,
      headers: {
        authorization: `<PERSON><PERSON><PERSON><PERSON> ${process.env.PERMISSIONS_AUTH}`,
      },
    });
  }

  /**
   * Sends an authorization request to check if a user is allowed to perform an action.
   * @param {string} action - The action to authorize.
   * @param {string} username - The username requesting the action.
   * @returns {Promise<object>} - The response data from the permissions API.
   */
  async authorize(action, username) {
    try {
      const permission = await this.client.post(`/authorize`, {
        action: action,
        username: username,
      });
      return permission.data;
    } catch (error) {
      throw error;
    }
  }
  
}

exports.PermissionService = PermissionService;

