const axios = require("axios").default;

class ClientService {
  constructor({ baseUrl, apiKey }) {
    this.apiKey = apiKey;
    this.baseURL = baseUrl;
    this.mambuClient = axios.create({
      baseURL: baseUrl,
      headers: {
        Accept: "application/vnd.mambu.v2+json",
        ApiKey: apiKey,
      },
    });

    this.mambuClientv1 = axios.create({
      baseURL: baseUrl,
      headers: {
        Accept: "application/json",
        ApiKey: apiKey,
      },
    });
  }

  // Get Mambu user details
  async getUser(encodedKey) {
    try {
      const client = await this.mambuClient.get(
        `/users/${encodedKey}?fullDetails=true`
      );
      return client.data;
    } catch (error) {
      console.error(
        "Error fetching user details:",
        error.response ? error.response.data : error.message
      );
      throw error;
    }
  }

  async getLoanById(id) {
    try {
      const loan = await this.mambuClient.get(`loans/${id}/?detailsLevel=FULL`);
      return loan.data;
    } catch (error) {}
  }

  async getClientById(id) {
    try {
      const client = await this.mambuClientv1.get(`/clients/${id}`);
      return client.data;
    } catch (error) {
      throw error;
    }
  }

  async getLoanSchedule(loanAccountId) {
    try {
      const schedule = await this.mambuClientv1.get(
        `/loans/${loanAccountId}/repayments`
      );
      return schedule.data;
    } catch (error) {
      throw error;
    }
  }

  async getClientDeposits(accountHolderKey) {
    try {
      const searchData = {
        filterCriteria: [
          {
            field: "accountHolderKey",
            operator: "EQUALS_CASE_SENSITIVE",
            value: accountHolderKey,
          },
        ],
        sortingCriteria: {
          field: "id",
          order: "DESC",
        },
      };

      const clientdeposits = await this.mambuClient.post(
        `/deposits:search?detailsLevel=FULL`,
        searchData
      );
      return clientdeposits.data;
    } catch (error) {
      console.log({
        error: JSON.stringify(error.response.data, null, 3),
      });
    }
  }

  async getLoanTransaction(accountId) {
    const searchData = {
      filterCriteria: [
        {
          field: "parentAccountID",
          operator: "EQUALS_CASE_SENSITIVE",
          value: accountId,
        },
        {
          field: "type",
          operator: "EQUALS_CASE_SENSITIVE",
          value: "DEPOSIT",
        },
        {
          field: "wasAdjusted",
          operator: "EQUALS_CASE_SENSITIVE",
          value: "NO",
        },
      ],
      sortingCriteria: {
        field: "creationDate",
        order: "DESC",
      },
    };

    return this.mambuClient.post(`/deposits/transactions:search`, searchData);
  }

  async getUserById(id) {
    const client = await this.mambuClientv1.get(`/users/${id}`);
    return client.data;
  }

  async getDepositId(accountHolderKey) {
    try {
      const searchData = {
        filterCriteria: [
          {
            field: "accountHolderKey",
            operator: "EQUALS_CASE_SENSITIVE",
            value: accountHolderKey,
          },
        ],
      };

      const depositId = await this.mambuClient.post(
        "/deposits:search?detailsLevel=FULL",
        searchData
      );
      return depositId.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllDeposits(accountHolderKey) {
    try {
      const deposits = await this.getDepositId(accountHolderKey);
      return deposits;
    } catch (error) {
      throw new Error(error);
    }
  }

  async searchLoans(loanName, { limit, offset }) {
    const searchData = {
      filterCriteria: [
        {
          field: "accountHolderKey",
          operator: "EQUALS_CASE_SENSITIVE",
          value: accountHolderKey,
        },

        {
          field: "loanName",
          operator: "STARTS_WITH",
          value: loanName,
        },
      ],
    };

    try {
      const loans = await this.mambuClient.post(`/loans:search`, searchData, {
        params: {
          paginationDetails: "ON",
          limit: limit,
          offset: offset,
        },
      });
      return loans;
    } catch (error) {
      throw new Error(error);
    }
  }
}

module.exports = ClientService;
