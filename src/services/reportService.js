const db = require("../db/models");
const sequelize = require("sequelize");
const ReportModel = db.Reports;
const FileModel = db.Files;

function lettersOnly(str) {
  return str
    .replace(/[^a-zA-Z0-9]/g, "")
    .toLowerCase()
    .trim();
  //or return str.match(/[a-z]/gi).join('');
  //or return str.replace(/[^a-z]/gi,"");
}

class ReportService {
  //create file

  async createFile(payload) {
    console.log({
      payload,
    });
    let fileName = lettersOnly(payload.fileName);
    let realFileName = lettersOnly(payload.realFileName);
    const count = await this.findSimilarFile(realFileName);

    if (count > 0) {
      throw new Error("fileName" + fileName + "exists");
    }

    return await FileModel.create(
      { ...payload, realFileName },
      {
        returning: true,
        raw: true,
      }
    );
  }

  //find similar file name

  async findSimilarFile(fileName) {
    const lookupValue = lettersOnly(fileName);

    const { count } = await FileModel.findAndCountAll({
      where: {
        realFileName: {
          [sequelize.Op.iLike]: lookupValue + "%",
        },
      },
    });
    return count;
  }

  // find file name

  async fetchFiles(params) {
    return FileModel.findAll({
      where: {
        ...params,
      },
    });
  }

  // find by id reports

  async insertReport(fileId, params) {
    return ReportModel.create(
      {
        ...params,
        fileId,
      },
      {
        returning: true,
        raw: true,
      }
    );
  }

  async bulkInsertReport(reports) {
    return ReportModel.bulkCreate(reports);
  }

  async deleteFile(id) {
    return FileModel.destroy({
      where: {
        id,
      },
    });
  }

  // fetch reports by file id
  async fetchFileStats() {
    return ReportModel.findAndCountAll({
      include: [
        {
          model: FileModel,
          required: true,
        },
      ],
      group: "fileId",
    });
  }

  async fetchInvoiceReports(fileId) {
    return ReportModel.findAll({
      where: {
        fileId,
      },
      include: {
        model: FileModel,
        attributes: ["fileName", "realFileName"],
        as: "file",
      },
    });
  }

  // update file

  async updateFileById(id, params) {
    return FileModel.update(params, {
      where: {
        id,
      },
      returning: true,
      plain: true,
    });
  }

  async getFileById(id) {
    return FileModel.findOne({
      where: {
        id,
      },
    });
  }

  async updateReportById(id, params) {
    return ReportModel.update(params, {
      where: {
        id,
      },
      returning: true,
      plain: true,
    });
  }

  async incrementReportedCount(fileId, add) {
    return FileModel.increment(
      { reportedCount: add },
      { where: { id: fileId } }
    );
  }

  async deleteFile(fileId) {
    return FileModel.destroy({
      where: {
        id: fileId,
      },
    });
  }

  // clear reports by file name
  async deleteReports(fileId) {
    return ReportModel.destroy({
      where: {
        fileId,
      },
    });
  }

  async getTask() {
    return ReportModel.findAll({
      where: {
        status: 0,
      },
      limit:   5,
      order: [["id", "ASC"]],
    })
      .then((response) => {
        return JSON.parse(JSON.stringify(response));
      })
      .catch((err) => {
        return null;
      });
  }
  
}

module.exports = new ReportService();
