const ReportService = require("#services/reportService");
const ClientService = require("#services/clientService");

const getConfig = require("../config");

const pdfVal = {};
pdfVal.insertRecords = async function ({
  records,
  baseUrl,
  sub,
  environment,
  fileId,
  fileName,
}) {
  const mappedRecords = records.map((item) => {
    console.log({
      item,
    });
    const [accountId, depositName,clientName,balance] = item;

    let data = { 
      accountId: accountId + "",
      depositName: depositName + "",
      clientName: clientName + "",
      balance: balance + "",
      environment,
      sub,
      baseUrl,
      processed: false,
      fileId,
      fileName,
      status: 0,
    };

    return data;
  });
  console.log({
    mappedRecords
  });
  return ReportService.bulkInsertReport(mappedRecords);
};

pdfVal.processTask = async function (task) {
  const { accountId, depositName, id, environment, sub, baseUrl, fileId } = task;
  const config = getConfig({ mambu_env: environment, baseUrl, sub });
  const clientService = new ClientService(config);
  const { hasValue, data } = await clientService(accountId, depositName);

  console.log({
    dataProcess: data,
  });

  if (!hasValue) {
    return await pdfVal.updateFileAndReport({
      fileId,
      add: 1,
      reportId: id,
      params: {
        status: "failed",
        processed: true,
        ...data,
      },
    });
  }

  const updateUpdate = await pdfVal.updateFileAndReport({
    fileId,
    add: 1,
    reportId: id,
    params: {
      status: "attached",
      processed: true,
    },
  });

  return "done";
};

pdfVal.updateFileAndReport = async function ({
  fileId,
  add,
  params,
  reportId,
}) {
  const updateFile = ReportService.incrementReportedCount(fileId, add);
  const updateRecord = ReportService.updateReportById(reportId, {
    ...params,
  });

  return Promise.allSettled([updateFile, updateRecord]);
};

module.exports = pdfVal;
