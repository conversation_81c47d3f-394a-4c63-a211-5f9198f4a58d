const path = require("path");
const fs = require("fs");
const xl = require("excel4node");

const formmatDate = (date = "") => {
    return new Date(date).toLocaleString();
}

const formatNumber = (num) => {
    return num;
}

const readPdf = (pdfName, extn = "pdf") => {
    try {
        const file = fs.readFileSync(
            path.join(__dirname, `../uploads${pdfName}.${extn}`),
            { encoding: "base64" }
        );
        return file;
    } catch (error) {
        console.log(error);
    }
};

const generateExcel = async (repayment = [], reportName = "") => {
    repayment = repayment.map(data => {
        return {
            accountId: data.accountId,
            depositName: data.depositName,
            clientName: data.clientName,
            balance: data.balance,
            LatestdepositDate: data.LatestdepositDate,
            createdAt: new Date(data.createdAt).toLocaleString()

        }
    })
    const headers = [
        "Account ID",
        "Deposit Account Name",
        "Client Name",
        "Balance",
        "Latest Deposit Date",
        "Date Report Generated",
    ];

    let options = {
        pageSetup: {
            orientation: "landscape",
        },
    };

    const wb = new xl.Workbook();
    let ws = wb.addWorksheet("Collections", options);

    // Create a reusable style
    var style = wb.createStyle({
        font: {
            color: "black",
            size: 12,
        },
        numberFormat: "$#,##0.00; ($#,##0.00); -",
    });

    let headerStyle = wb.createStyle({
        font: {
            color: "black",
            bold: true,
            size: 15,
        },
        background: "",
    });

    const countStyle = {
        font: {
            color: "#2f75b5",
            bold: true,
            size: 13,
        },
    };

    let ribbonStyle = {
        fill: {
            type: "pattern",
            fgColor: "#ddebf7",
            patternType: "solid",
        },
    };

    let cellHeaderStyles = wb.createStyle({
        font: {
            color: "white",
            bold: true,
            size: 14,
        },
        fill: {
            type: "pattern",
            fgColor: "#2f75b5",
            patternType: "solid",
        },
    });

    // Set value of cell A1 to 100 as a number type styled with paramaters of style
    ws.cell(1, 1, 1, 6, true)
        .string("Monthly Deposits Report")
        .style(headerStyle);

    ws.cell(2, 1, 2, 6, true)
        .string(`Deposits Report`)
        .style(headerStyle);

    ws.cell(3, 1, 3, 6, true)
        .string(`Report generated on ${formmatDate(new Date())}`)
        .style(headerStyle);

    // // Set value of cell A2 to 'string' styled with paramaters of style
    ws.cell(4, 1).string("").style(style);
    let cellPos = 5;
    let columnPos = 1;

    ws.cell(cellPos, 1).string(`Count ${repayment.length}`).style(countStyle);
    cellPos += 1;

    for (let i = 0; i < headers.length; i++) {
        ws.cell(cellPos, columnPos).string(headers[i]).style(cellHeaderStyles);
        ws.column(columnPos).setWidth(20);
        columnPos += 1;
    }

    cellPos += 1;
    columnPos = 1;
    if (repayment.length) {
        let keys = Object.keys(repayment[0]);
        for (let i = 0; i < repayment.length; i++) {
            for (let j = 0; j < keys.length; j++) {
                ws.cell(cellPos, columnPos)
                    .string(
                        "" +
                        (typeof repayment[i][keys[j]] === "number"
                            ? formatNumber(Math.ceil(repayment[i][keys[j]])) >= 0
                                ? formatNumber(Math.ceil(repayment[i][keys[j]]))
                                : "-" + formatNumber(Math.ceil(repayment[i][keys[j]]))
                            : repayment[i][keys[j]])
                    )
                    .style(i % 2 == 0 ? ribbonStyle : style);
                columnPos += 1;
            }
            columnPos = 1;
            cellPos += 1;
        }
    }

    // ws.cell(cellPos, 5)
    //   .string("Total")
    //   .style(style)
    //   .style({ font: { size: 14 } });
    // ws.cell(cellPos, 6)
    //   .string("" + details.repaymentTotalDue.toFixed(2))
    //   .style(style)
    //   .style({ font: { size: 14 } });

    // collections
    cellPos += 1;

    //end
    const buffer = await wb.writeToBuffer();
    fs.writeFileSync(path.join(__dirname, `../uploads/${reportName}.xlsx`), buffer, "utf8");
    return buffer.toString("base64");
};

module.exports = { generateExcel }