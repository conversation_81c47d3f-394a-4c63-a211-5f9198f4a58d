var fs = require("fs");
var xlsx = require("read-excel-file/node");
const XLSX = require("xlsx");

function convertWithXlsx(pathToExcel, outputCsvPath) {
  return new Promise((resolve, reject) => {
    //looping through all sheets
    console.log({
      pathToExcel,
      outputCsvPath,
    });

    var workbook = XLSX.readFile(pathToExcel);
    const sheet_list = workbook.SheetNames; // get sheet list names
    const tt = XLSX.utils.sheet_to_csv(workbook.Sheets[sheet_list[0]], {
      blankrows: true,
    });
    // const tt2 = XLSX.utils.sheet_to_json(workbook.Sheets[sheet_list[0]], {
    //   blankrows: true
    // });

    console.log({
      // wb: JSON.stringify(workbook, null, 3),
      tt,
      sheet_list,
      // tt2,
    });

    return fs.writeFile(outputCsvPath, tt, function (err) {
      if (err) {
        return reject(err);
      }
      console.log("csv was saved in the current directory!");
      return resolve(true);
    });
  }).catch((err) => {
    reject(err);
  });
}

function convertWithRead(pathToExcel, outputCsvPath) {
  return new Promise((resolve, reject) => {
    //looping through all sheets
    console.log({
      pathToExcel,
      outputCsvPath,
    });
    var rows = [];
    var writeStr = "";

    return xlsx(pathToExcel)
      .then((roows) => {
        console.log(roows);
        if (roows.length == 0) {
          return false;
        }

        for (var i = 0; i < roows.length; i++) {
          writeStr += roows[i].join(",") + "\n";
        }

        fs.writeFile(outputCsvPath, writeStr, function (err) {
          if (err) {
            return reject(err);
          }
          console.log("csv was saved in the current directory!");
          return resolve(true);
        });
      })
      .catch((err) => {
        return false;
      });

    //writes to a file, but you will presumably send the csv as a
    //response instead
  });
}

async function sheet_to_csv(pathToExcel, outputCsvPath) {
  try {
    const try_1 = await convertWithRead(pathToExcel, outputCsvPath);

    if (!try_1) {
      return await convertWithXlsx(pathToExcel, outputCsvPath);
    }
  } catch (error) {
    throw new Error("unable to convert file to csv");
  }
}

module.exports = sheet_to_csv;
