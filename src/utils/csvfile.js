const fs = require("fs");
const path = require("path");

const generateCsv = (result_list) => {
    const pathtocsv = path.join(__dirname, "../uploads/reports.csv")
    const headers = Object.keys(result_list[0]).join(",") + "\n"
        ;

    let csv = "";
    csv += headers;

    console.log({ result_list })

    for (var i = 0; i < result_list.length; i++) {
        var tx = result_list[i];

        csv += Object.values(tx).join(",") + "\n";
    }

    return new Promise((resolve, reject) => {
        return fs.writeFile(pathtocsv, csv, function (err) {
            if (err) {
                return reject(err)
            }
            return resolve(pathtocsv)
        })
    }).catch(err => {

        reject(err)

    })
    return csv;
};

module.exports = generateCsv;
