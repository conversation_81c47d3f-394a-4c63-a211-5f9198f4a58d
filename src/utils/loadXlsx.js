const fs = require("fs");
const xlsx = require("xlsx");
const { promisify } = require("util");
const unlinkFile = promisify(fs.unlink);
const writeFile = promisify(fs.writeFile);
const path = require("path");

// step 1
const loadXlsx = function ({ sheet_index = 0, filename }) {
  const pathToExcel = "uploads/" + filename;
  try {
    let workbook = xlsx.readFile(pathToExcel);
    const sheet_list = workbook.SheetNames; // get sheet list names
    const sheet_to_json = xlsx.utils.sheet_to_json(
      workbook.Sheets[sheet_list[sheet_index]]
    );

    return { filename, data: sheet_to_json };
  } catch (error) {
    throw error;
  }
};

// step 2 write file to json
const saveFileToJson = function (filename, list) {
  const dataJson = JSON.stringify(list, null, 4);
  const _toSave = "uploads/" + filename + ".json";
  fs.writeFileSync(_toSave, dataJson, (err) => {
    if (err) throw new Error(err);
    console.log("File written succesfully");
  });
};

//step 3 load json data from file
const loadJson = function (filename) {
  /***
   * read already generated file and return the data
   * filename e.g "./xlsx.json"
   */
  const _toSave = "uploads/" + filename + ".json";
  try {
    const dataBuffer = fs.readFileSync(_toSave);
    const dataJson = dataBuffer.toString();
    return JSON.parse(dataJson);
  } catch (error) {
    // if no file
    throw error;
  }
};

//step 4 sanitizes the excel data
const sanitize = function (list) {
  // getting ["1", "Baringo County"]
  const controlObjKeys = Object.keys({ ...list[0] });
  const _sanitized = [];

  for (const item of list) {
    let center = {
      name: item[controlObjKeys[1]],
    };

    _sanitized.push(center);
  }

  return _sanitized;
};

// step 5 unlink file from system
// step 5 unlink file from system
const deleteFile = function (filename, location = false, printer = false) {
  if (printer) {
    return unlinkFile(filename)
      .then(() => {
        return "ok";
      })
      .catch(() => {
        return "bypass delete error";
      });
  }

  const toBeRemoved = location
    ? path.join(__dirname, "../../uploads/" + filename)
    : path.join(__dirname, "../views/docs/" + filename);
  return unlinkFile(toBeRemoved);
};

const sheetToCsv = function (sheet, filename) {
  console.log("sheet file csv", filename);
  const pathToSave = path.join(__dirname, "../../uploads/" + filename + ".csv");
  const csv = xlsx.utils.sheet_to_csv(sheet);

  console.log("sheet file csv");
  return writeFile(pathToSave, csv).then(() => {
    return pathToSave;
  });
};

const loadFile = function (filename) {
  const sheet_index = 0;
  const pathToExcel = path.join(__dirname, "../../uploads/" + filename);
  let workbook = xlsx.readFile(pathToExcel);
  const sheet_list = workbook.SheetNames; // get sheet list names

  console.log("loading file xlsx");
  // return first sheet only
  const d = workbook.Sheets[sheet_list[sheet_index]];
  console.log(d);
  return d;
};


module.exports = {
  loadXlsx,
  saveFileToJson,
  loadJson,
  sanitize,
  deleteFile,
  sheetToCsv,
  loadFile,
};
