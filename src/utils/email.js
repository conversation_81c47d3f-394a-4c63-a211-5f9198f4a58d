require("dotenv").config();
const sgMail = require("@sendgrid/mail");

const apikey = process.env.sendgrid_apiKey

sgMail.setApiKey(apikey);

module.exports = async function sendNotifactionToallUsers(
  emailData,
  attachment
) {
  let info = await sgMail
    .send({
      // from: 'smtp.ethereal.email',
      from: `${"DEPOSITS"} <${"<EMAIL>"}>`,
      to: emailData.to_who,
      // cc:['<EMAIL>', ],
      
      attachments: attachment,
      subject: "DEPOSIT ACCOUNT REPORT",
      html: emailData.message,
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      console.log(error.response.body.errors, "*******************");
    });

  return info;
};



