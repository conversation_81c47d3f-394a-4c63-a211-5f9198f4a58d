function extractMambuUser(signed_request) {
  try {
    // split the base64 grab first item
    console.log({ signed_request });
    let _signed = signed_request.split(".")[1];

    // decode
    const data = Buffer.from(_signed, "base64").toString("utf8");

    // parse JSON
    const decode_obj = JSON.parse(data);
    const userId = decode_obj["USER_KEY"];
    const domain = decode_obj["DOMAIN"];
    const sub = decode_obj["TENANT_ID"];
    const loanID = decode_obj?.OBJECT_ID;

    const mambu_env = domain.includes("sandbox") ? "sandbox" : "production";
    const base_url = "https://" + domain + "/api";

    return { userId, base_url, mambu_env, sub, loanID };
  } catch (error) {
    // Display a custom error message
    throw new Error("Failed to extract Mambu user information. Please check the signed_request format.");
  }
}

module.exports = extractMambuUser;
