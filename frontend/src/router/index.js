import Vue from "vue";
import VueRouter from "vue-router";
import Home from "../views/Home.vue";
// import Compute from "../views/Compute.vue";
import FileUpload from "../components/FileUpload.vue";

Vue.use(VueRouter);

const routes = [
  {
    path: "/deposits",
    name: "Home",
    component: Home,
  },
  {
    path: "/",
    redirect: "/deposits",
  },
  // {
  //   path: "/compute",
  //   redirect: Compute,
  // },
  {
    path: "/ability",
    component: FileUpload,
  },

  {
    path: "*",
    name: "401",
    component: () => import(/* webpackChunkName: "about" */ "../views/401.vue"),
  },
];

const router = new VueRouter({
  routes,
});

export default router;
