import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);
export default new Vuex.Store({
  state: {
    loader: false,
    reports: [],
    roles: [],
    modalState: {
      value: false,
      message: "",
      color: "#C2185B",
    },
  },
  mutations: {
    setUser(state, payload) {
      state.user = payload;
    },

    setReportsData(state, reports) {
      state.reports = reports;
    },

    setLoading(state, payload) {
      state.loader = payload;
    },

    toggleModal(state, payload) {
      state.modalState = {
        value: !state.modalState.value,
        ...payload,
      };
    },
    toggleSearch(state, payload) {
      state.isSearching = payload;
    },
  },
  actions: {
    isLoading({ commit }, payload) {
      commit("setLoading", payload);
    },

    toggleModal({ commit }, message) {
      commit("toggleModal", message);
    },

    toggleSearch({ commit }, payload) {
      commit("toggleSearch", payload);
    },
  },
  getters: {
    rolesPresent: (state) => state.roles.length > 0,
    getUser: (state) => state.user,
  },
});
