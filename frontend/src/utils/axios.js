import axios from "axios";
import config from "../config";
import store from "@/store";
import router from "@/router";
const { handleError } = require("./handleError");

let mambuUser = localStorage.getItem("mambuUser");

const instance = axios.create({ 
  baseURL: config["BACKEND_SERVICE"],
  headers: {
    mambuUser,
  },
});

// Add a request interceptor
instance.interceptors.request.use(
  function (config) {
    store.dispatch("isLoading", true);
    return config;
  },
  function (error) {
    store.dispatch("isLoading", false).then(() => {
      store.dispatch("toggleModal", error.message || error);
    });
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
instance.interceptors.response.use(
  function (response) {
    store.dispatch("isLoading", false);
    return response;
  },
  function (error) {
  console.log(error);
    if (
      error.response &&
      (error.response.status == "401" || error.response.status == "403")
    ) {
      router.replace({
        name: "401",
      });
    }

    store.dispatch("isLoading", false).then(() => {
      const message = handleError(error.response);
      store.dispatch("toggleModal", message || error);
    });

    return Promise.reject(error);
  }
);

export default instance;
