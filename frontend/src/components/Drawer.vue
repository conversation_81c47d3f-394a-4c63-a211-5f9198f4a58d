<template>
  <v-navigation-drawer app v-model="drawerLocal" permanent>
    <!-- Header Section with App Title -->
    <v-list-item>
      <v-list-item-content>
        <v-list-item-title class="text-h6"> Platinum Kenya </v-list-item-title>
        <v-list-item-subtitle> Deposit Accounts Reports</v-list-item-subtitle>
      </v-list-item-content>
    </v-list-item>

    <v-divider></v-divider>

     <!-- Navigation Items based on Role (Admin/Normal User) -->
     <v-list dense nav>
      <v-list-item
        v-for="item in items"
        :key="item.title"
        link
        :to="item.to"
        class="drawer-item"
      >
        <v-list-item-icon>
          <v-icon>{{ item.icon }}</v-icon>
        </v-list-item-icon>

        <v-list-item-content>
          <v-list-item-title>{{ item.title }}</v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-navigation-drawer>
</template>

<script>
export default {
  props: ["drawer"],

  watch: {
    // Watch the `drawer` prop and update the local `drawerLocal` state
    drawer: {
      immediate: true,
      handler(val) {
        this.drawerLocal = val;
      },
    },
  },

  computed: {
    // Check if the user is an admin (getter from Vuex store)
    isAdmin() {
      return this.$store.getters.isAdmin;
    },

    // Return items based on the user's role (admin or normal)
    items() {
      return this.isAdmin ? this.itemsAdmin : this.itemsNormal;
    },

    // Two-way binding for the drawer component
    drawerLocal: {
      get() {
        return this.drawer;
      },
      set(val) {
        this.$emit("update:drawer", val); // Emit the updated state to the parent
      },
    },
  },

  data() {
    return {
      // Admin-specific navigation items
      itemsAdmin: [
        {
          title: "Uploads and Reports",
          icon: "mdi-file-upload",
          to: "/deposits",
        },
        // Uncomment this item if you want to add the "compute" functionality back
        // {
        //   title: "Compute",
        //   icon: "mdi-calculator",
        //   to: "/compute",
        // },
      ],
      // Normal user navigation items
      itemsNormal: [
        {
          title: "Upload and Reports",
          icon: "mdi-file-upload",
          to: "/deposits",
        },
        // Uncomment this item if you want to add the "compute" functionality back
        // {
        //   title: "Compute",
        //   to: "/compute",
        //   icon: "mdi-calculator",
        // },
      ],
    };
  },
};
</script>

<style scoped>
/* Style the drawer items for a cleaner look */
.drawer-item {
  padding: 10px 16px;
  transition: background-color 0.2s ease;
}

.drawer-item:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.text-h6 {
  font-weight: 600;
}

.v-list-item-subtitle {
  font-size: 0.9rem;
  color: #6c757d;
}
</style>
