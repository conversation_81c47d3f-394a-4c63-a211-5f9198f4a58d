<template>
  <v-container class="grey lighten-5">
    <v-row no-gutters>
      <v-col cols="12">
        <v-card class="pa-2" outlined tile>
          <!-- Success Alert -->
          <v-alert v-if="success" shaped outlined type="success" dismissible>
            File uploaded successfully
          </v-alert>

          <!-- Error <PERSON> -->
          <v-alert
            v-if="!success && errorMessage"
            shaped
            outlined
            type="error"
            dismissible
          >
            {{ errorMessage }}
          </v-alert>

          <!-- Switch to toggle between Excel upload or sample download -->
          <v-container class="px-0" fluid v-if="!isEditing">
            <v-switch
              v-model="uploadExcel"
              label="Upload Excel file instead"
            ></v-switch>
          </v-container>

          <!-- If not uploading Excel, display the sample download button -->
          <div v-if="!uploadExcel">
            <div class="download-btn">
              <v-btn href="https://deposits-apps.platinumcredit.co.ke/deposits/template" download>
                Download Sample
              </v-btn>
            </div>
          </div>

          <!-- Form for uploading Excel -->
          <form novalidate enctype="multipart/form-data" v-if="uploadExcel">
            <v-file-input
              @change="handleFileUpload"
              label="File input"
              outlined
              dense
              accept=".xlsx,.xls"
            ></v-file-input>
            <v-btn
              class="mr-4"
              @click="uploadFile"
              :loading="isUploading"
              :disabled="!file"
            >
              Upload
            </v-btn>
            <v-btn @click="close" class="ml-3" color="secondary">Close</v-btn>
          </form>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { validationMixin } from "vuelidate";
import { required } from "vuelidate/lib/validators";
import { inputClientData } from "../services/inputClientData";
import { handleError } from "../utils/handleError";
import UploadService from "../services/UploadService.js";

export default {
  name: "FileUpload",
  mixins: [validationMixin],
  // emits: ["done", "refetch"],
  watch: {
    editedItem: {
      immediate: true,
      handler(val) {
        this.isEditing = !!val.name; // Simplified check to set editing state
      },
    },
  },
  validations: {
    name: { required },
    checkbox: {
      checked(val) {
        return val; // Ensure this validation is correctly working
      },
    },
  },
  data: () => ({
    name: "",
    uploadExcel: true,
    success: false,
    file: null,
    isEditing: false,
    isUploading: false,
    errorMessage: null,
    loading: false,
  }),

  methods: {
    // Submit form data to the API
    submit() {
      this.$v.$touch();
      this.loading = true;
      inputClientData()
        .then((res) => {
          console.log(res);
          this.success = true;
          this.clear();
          this.$emit("refetch");
        })
        .catch((error) => {
          this.success = false;
          this.errorMessage = handleError(error.response);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // Reset form fields
    clear() {
      this.$v.$reset();
      this.name = "";
      this.isEditing = false;
      this.errorMessage = null;
      this.file = null;
    },

    // Close modal and clear the state
    close() {
      this.clear();
      this.$emit("done");
    },

    // Handle file input change
    handleFileUpload(event) {
      this.file = event;
    },

    // Upload the file to the server
    uploadFile() {
      if (!this.file) {
        return; // Return early if no file is selected
      }

      this.isUploading = true;

      UploadService.checkFileName({ fileName: this.file.name })
        .then(() => {
          return UploadService.upload(this.file, "/reportsUpload");
        })
        .then(() => {
          this.success = true;
          this.close();
        })
        .catch((error) => {
          this.success = false;
          this.errorMessage =
            error.message || "An error occurred during upload.";
        })
        .finally(() => {
          this.isUploading = false;
          this.$emit("refetch");
        });
    },
  },
};
</script>

<style scoped>
.download-btn {
  margin-bottom: 1.2em;
}
</style>
