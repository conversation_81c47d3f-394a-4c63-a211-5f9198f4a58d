<template>
  <v-data-table
    :headers="headers"
    :items="desserts"
    sort-by="calories"
    class="elevation-1"
    :loading="loading"
    v-if="!hideFilesTables"
    :footer-props="{
      'items-per-page-options': [10, 20, 30, 40, 50, 100],
    }"
    :items-per-page="30"
    :search="search"
  >
    <template v-slot:top>
      <v-toolbar flat>
        <v-toolbar-title>Files Uploaded</v-toolbar-title>
        <v-divider class="mx-4" inset vertical></v-divider>
        <v-btn icon @click.prevent="fetchReports">
          <v-icon color="primary">mdi-file-refresh</v-icon>
        </v-btn>
        <v-dialog v-model="dialog" max-width="600px" persistent>
          <template v-slot:activator="{ on, attrs }">
            <v-btn color="primary" class="ml-2" v-bind="attrs" v-on="on" small>
              <v-icon> mdi-upload </v-icon>
              Deposit Aging-accounts
            </v-btn>
          </template>

          <DepositUpload
            @done="close"
            @refetch="fetchReports"
            :editedItem="editedItem"
          />
        </v-dialog>
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
        <CsvDownload :data="desserts" label="filesUploaded" />

        <v-dialog v-model="dialogDelete" max-width="500px">
          <v-card>
            <v-card-title class="text-h5"
              >Are you sure you want to delete this item?</v-card-title
            >
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="blue darken-1" text @click="closeDelete"
                >Cancel</v-btn
              >
              <v-btn color="blue darken-1" text @click="deleteItemConfirm"
                >OK</v-btn
              >
              <v-spacer></v-spacer>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-toolbar>
    </template>
    <template v-slot:[`item.createdAt`]="{ item }">{{
      formatDate(item.createdAt)
    }}</template>

    <template v-slot:[`item.updatedAt`]="{ item }">{{
      formatDate(item.updatedAt)
    }}</template>
    <template v-slot:[`item.progress`]="{ item }">
      <v-progress-linear
        :value="getPercentage(item)"
        color="blue-grey"
        height="25"
      >
        <template>
          <strong>{{ getPercentage(item) }}%</strong>
        </template>
      </v-progress-linear>
    </template>
    <template v-slot:[`item.actions`]="{ item }">
      <v-icon small class="mr-2" @click="editItem(item)"> mdi-file</v-icon>
      <v-icon small @click="deleteItem(item)" color="red"> mdi-delete </v-icon>
    </template>
    <template v-slot:no-data>
      <v-btn color="primary" @click="fetchReports"> Refresh </v-btn>
    </template>
  </v-data-table>

  <!-- invoices tabe; -->
  <v-data-table
    :headers="headersInvoices"
    :items="invoices"
    sort-by="calories"
    class="elevation-1"
    :loading="loading"
    v-else
    :footer-props="{
      'items-per-page-options': [10, 20, 30, 40, 50, 100],
    }"
    :items-per-page="30"
    :search="search"
  >
    <template v-slot:top>
      <v-toolbar flat>
        <v-toolbar-title>Bulk DepositAging By File</v-toolbar-title>
        <v-divider class="mx-4" inset vertical></v-divider>
        <v-btn color="primary" @click.prevent="headBack" text
          >Back to Reports</v-btn
        >
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
        <CsvDownload :data="invoices" :label="invoicefileName" />
      </v-toolbar>
    </template>
    <template v-slot:[`item.createdAt`]="{ item }">{{
      formatDate(item.createdAt)
    }}</template>

    <template v-slot:[`item.updatedAt`]="{ item }">{{
      formatDate(item.updatedAt)
    }}</template>
  </v-data-table>
</template>
  
<script>
import DepositUpload from "@/components/FileUpload.vue";
import UploadService from "@/services/UploadService.js";
import CsvDownload from "@/components/CsvDownload.vue";
import dayjs from "dayjs";


export default {
  name: "ReportsTable",
  components: {
    DepositUpload,
    CsvDownload
  },

  mounted() {
    this.fetchReports();
  },


  data: () => ({
    dialog: false,
    loading: false,
    hideFilesTables: false,
    dialogDelete: false,
    invoicefileName: "",
    search: "",
    headers: [
      {
        text: "id",
        align: "start",
        sortable: false,
        value: "id",
      },
      { text: "FileName", value: "realFileName" },
      { text: "status", value: "status" },
      // { text: "progress", value: "progress" },

      { text: "uploadedByEncodedKey", value: "uploadedByEncodedKey" },
      { text: "Environment", value: "environment", sortable: false },
      { text: "Subsidiary", value: "sub", sortable: false },
      { text: "UploadedCount", value: "uploadedCount", sortable: false },
      { text: "ReportedCount", value: "reportedCount", sortable: false },

      { text: "Actions", value: "actions", sortable: false },
      { text: "created At", value: "createdAt" },
      { text: "updatedAt", value: "updatedAt" },
    ],
    desserts: [],
    invoices: [],
    headersInvoices: [
      {
        text: "id",
        align: "start",
        sortable: false,
        value: "id",
      },
      { text: "Deposit Account ID", value: "accountId" },
      { text: "Deposit Account Name", value: "depositName" },
      { text: "Client Name", value: "clientName" },
      { text: "Balance", value: "balance" },
      { text: "Latest Deposit Date", value: "LatestdepositDate" },
    ],
    editedIndex: -1,
    editedItem: {
      name: "",
      calories: 0,
      fat: 0,
      carbs: 0,
      protein: 0,
    },
    defaultItem: {
      name: "",
      calories: 0,
      fat: 0,
      carbs: 0,
      protein: 0,
    }
  }),
  methods: {
    formatDate(date) {
      return dayjs(date).format("DD-MM-YYYY:HH:mm:ss:SSS");
    },

    getPercentage(item) {
      return Math.ceil((item.reportedCount / item.uploadedCount) * 100);
    },
    fetchReports() {
      return UploadService.getReports().then((data) => {
        console.log({
          data,
        });
        this.desserts = data;
      });
    },
    headBack() {
      this.hideFilesTables = false;
      this.fetchReports();
      this.invoicefileName = "";
    },

    editItem(item) {
      this.editedIndex = this.desserts.indexOf(item);
      this.editedItem = Object.assign({}, item);
      this.loading = true;
      this.invoicefileName = this.editedItem.realFileName;

      return UploadService.fetchInvoiceReports(this.editedItem.id)
        .then((data) => {
          this.invoices = data;
          console.log({
            inc: this.invoices[0],
          });
          this.hideFilesTables = true;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    deleteItem(item) {
      this.editedIndex = this.desserts.indexOf(item);
      this.editedItem = Object.assign({}, item);
      console.log({
        i: this.editedItem,
      });
      this.dialogDelete = true;
    },

    deleteItemConfirm() {
      return UploadService.deleteReport(this.editedItem.id)
        .then(() => {
          this.fetchReports();
        })
        .finally(() => {
          this.closeDelete();
        });
    },

    close() {
      this.dialog = false;
      this.$nextTick(() => {
        this.editedItem = Object.assign({}, this.defaultItem);
        this.editedIndex = -1;
      });
    },

    closeDelete() {
      this.dialogDelete = false;
      this.$nextTick(() => {
        this.editedItem = Object.assign({}, this.defaultItem);
        this.editedIndex = -1;
      });
    },

    save() {
      if (this.editedIndex > -1) {
        Object.assign(this.desserts[this.editedIndex], this.editedItem);
      } else {
        this.desserts.push(this.editedItem);
      }
      this.close();
    },
  },
  
};
</script>
  
  
  

