<template>
  <v-row justify="center">
    <v-col cols="auto">
      <v-dialog
        transition="dialog-bottom-transition"
        max-width="600"
        v-model="dialog"
      >
        <v-card>
          <!-- Modal Header with Title -->
          <v-toolbar color="#C2185B" dark>
            <span class="headline">{{ title }}</span>
            <v-spacer></v-spacer>
            <v-btn icon @click="closeModal">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-toolbar>

          <!-- Modal Content -->
          <v-card-text>
            <div class="text-h4 pa-12">{{ message }}</div>
          </v-card-text>

          <!-- Modal Actions -->
          <v-card-actions class="justify-end">
            <v-btn text @click="closeModal">Close</v-btn>
            <v-btn color="primary" @click="onConfirm">Confirm</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: "Modal",
  computed: {
    dialog() {
      return this.$store.state.modalState.value;
    },
    message() {
      return this.$store.state.modalState.message;
    },
    title() {
      return this.$store.state.modalState.title || "Alert";
    },
  },
  methods: {
    closeModal() {
      this.$store.dispatch("toggleModal", { value: false, message: "", title: "" });
    },
    onConfirm() {
      // You can perform any action here before closing the modal
      this.closeModal();
    },
  },
};
</script>

<style scoped>
.headline {
  font-size: 24px;
  font-weight: bold;
}
</style>
