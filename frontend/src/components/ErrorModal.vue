<template>
  <transition name="fadeAway">
    <div class="card errorMessage" v-if="this.$store.state.error">
      <h2 class="card-title">
        Error
      </h2>
      <p class="p-left p-right">
        {{ this.$store.state.error }}
      </p>
      <div class="card-actions">
        <button
          class="dismiss btn-red"
          @click.prevent="dismiss"
          aria-label="Dismiss Error"
        >
          Dismiss Error
        </button>
      </div>
    </div>
  </transition>
</template>



<script>
  export default {
    name: "ErrorMessage",
    methods: {
      dismiss() {
        this.$store.state.error = "";
      },
    },
  };
</script>