<template>
  <vue-json-to-csv
    :json-data="data"
    :csv-title="label"
    @error="(val) => handleError(val)"
    class="ml-3"
  >
    <v-icon right> mdi-download </v-icon>
  </vue-json-to-csv>
</template>

<script>
import VueJsonToCsv from "vue-json-to-csv";
export default {
  name: "CSv",
  props: ["data", "label"],
  components: {
    VueJsonToCsv,
  },
  methods: {
    handleError(val) {
      this.$store.dispatch("triggerModal", val);
    },

    handleSuccess(val) {
      // @success="(val) => handleSuccess(val)"
      console.log(val);
    },
  },
};
</script>

<style>
</style>