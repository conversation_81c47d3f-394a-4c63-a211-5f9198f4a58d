import axios from "axios";
class UploadService {
  constructor() { }
  static async upload(file, route = "/cars/") {
    let formData = new FormData();
    formData.append("excelfile", file);
    const res = await axios.post(route, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      //nice to have upload progress handler
    });
    return res.data;
  }
  static async checkFileName(payload) {
    const res = await axios.post("/reportsUpload/checkFileName", payload);
    return res.data;
  }
  static async getReports() {
    const reports = await axios.get("/reportsUpload/reports");
    console.log(reports, "reports");
    return reports.data;
  }
  static async deleteReport(id) {
    const reports = await axios.delete(`/reportsUpload/reports/${id}`);
    return reports.data;
  }
  static async fetchInvoiceReports(id) {
    const reports = await axios.get(`/reportsUpload/reports/invoices/${id}`);
    return reports.data;
  }
}

export default UploadService;
