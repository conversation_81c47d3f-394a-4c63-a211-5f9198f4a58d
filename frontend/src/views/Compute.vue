<template>
  <v-container> <p></p></v-container>
</template>

<script>
// @ is an alias to /src
import ComputeAppService from "../services/compute.service.js";
export default {
  name: "Home",
  components: {},
  mounted() {
    ComputeAppService.computeApp()
      .then(({message}) => {
        this.$store.dispatch("toggleModal", message);
      })
      .catch(() => {
        console.log("err here");
        this.$store.dispatch(
          "toggleModal",
          "oops, something went wrong, retrying..."
        );
        return ComputeAppService.computeApp();
      })
      .then(({message}) => {
        this.$store.dispatch("toggleModal", message.message);
      })
  },
};
</script>