<template>
  <v-app id="inspire">
    <!-- Drawer with new UI design -->
    <Drawer :drawer.sync="drawer" />

    <!-- App Bar with a modern gradient and enhanced toolbar -->
    <v-app-bar app color="primary" dark flat>
      <v-app-bar-nav-icon @click="toggleDrawer" />
      <v-toolbar-title class="font-weight-bold">AUTOMATED EMAIL REPORTS FOR DEPOSIT ACCOUNTS</v-toolbar-title>
      <v-spacer />
      <v-btn icon to="/deposits" class="ml-2" aria-label="Upload deposits" elevation="2">
        <v-icon>mdi-file-upload</v-icon>
      </v-btn>
      <!-- <v-btn icon to="/compute" class="ml-2" aria-label="Compute" elevation="2">
        <v-icon>mdi-calculator</v-icon>
      </v-btn> -->
    </v-app-bar>

    <!-- Main content area with updated background -->
    <v-main>
      <v-container fluid>
        <router-view />
        <Loader />
        <Modal />
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
import Loader from "./components/Loader.vue";
import Modal from "./components/Modal.vue";
import Drawer from "./components/Drawer.vue";

export default {
  name: "App",
  components: {
    Modal,
    Loader,
    Drawer,
  },
  data: () => ({
    drawer: false,
  }),
  methods: {
    toggleDrawer() {
      this.drawer = !this.drawer;
    },
  },
};
</script>

<!-- Modern UI styling -->
<style scoped>
#inspire {
  background: linear-gradient(135deg, #e0f7fa, #f5f5f5); /* Light gradient for modern feel */
  min-height: 100vh;
  color: #424242; /* Darker text for better contrast */
}

.v-toolbar-title {
  font-size: 26px; /* Modern and bold */
  letter-spacing: 0.5px;
}

.v-app-bar {
  background: linear-gradient(45deg, #0077c2, #00b8d4); /* Modern gradient for app bar */
}

.v-btn {
  border-radius: 8px; /* Rounded buttons for modern touch */
}

.v-btn > .v-icon {
  color: white; /* Ensuring contrast on icons */
}

/* Shadow effect for buttons */
.v-btn:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
