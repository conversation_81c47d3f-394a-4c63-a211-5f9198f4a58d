{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "cross-env NODE_ENV=prod VUE_APP_STAGE=prod vue-cli-service build --dest ../public/dist", "build:dev": "cross-env NODE_ENV=develop VUE_APP_STAGE=develop vue-cli-service build --dest ../public/dist", "dev": "cross-env BROWSER=none VUE_APP_STAGE=develop NODE_ENV=develop vue-cli-service serve", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.26.0", "core-js": "^3.6.5", "dayjs": "^1.11.7", "vue": "^2.6.11", "vue-json-to-csv": "^1.1.8", "vue-router": "^3.2.0", "vuelidate": "^0.7.7", "vuetify": "^2.4.0", "vuex": "^3.4.0"}, "devDependencies": {"@types/vuelidate": "^0.7.21", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-unit-jest": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/test-utils": "^1.0.3", "babel-eslint": "^10.1.0", "cross-env": "^10.0.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.4.5", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0"}}