{"name": "express-api-starter", "version": "1.2.0", "description": " A basic starter for an express.js API", "author": "<PERSON><PERSON> <PERSON>. <<EMAIL>> (https://w3cj.now.sh)", "scripts": {"lint": "eslint --fix src", "server": "nodemon src/index.js", "client": "cd frontend && npm run dev", "clientinstall": "npm start --prefix frontend", "dev": "concurrently \"npm run server\" \"npm run client\"", "start": "node src/index.js", "test": "mocha --exit"}, "main": "index.js", "dependencies": {"@sendgrid/mail": "^8.1.4", "@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-plugin-eslint": "^3.12.1", "@vue/cli-plugin-unit-jest": "^3.12.1", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "app-root-path": "^3.1.0", "axios": "^1.7.7", "busboy": "^1.6.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^10.0.0", "excel4node": "^1.8.2", "express": "^4.17.1", "helmet": "^4.6.0", "morgan": "^1.10.0", "node-schedule": "^2.1.0", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "read-excel-file": "^5.5.3", "sequelize": "^6.26.0", "uuid": "^9.0.0", "vue-json-to-csv": "^1.1.8", "vue-template-compiler": "^0.1.0", "vuelidate": "^0.7.7", "vuetify": "^2.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"concurrently": "^7.6.0", "cross-env": "^7.0.3", "eslint": "^7.28.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.23.4", "mocha": "^9.0.0", "nodemon": "^2.0.22", "sass": "~1.32.0", "sass-loader": "^10.0.0", "supertest": "^6.1.3", "vue-cli-plugin-vuetify": "~2.5.8", "vuetify-loader": "^1.7.0"}, "keywords": [], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/w3cj/express-api-starter.git"}, "imports": {"#src/*": "./src/*.js", "#api/*": "./src/api/*.js", "#controllers/*": "./src/controllers/*.js", "#routes/*": "./src/routes/*.js", "#services/*": "./src/services/*.js", "#utils/*": "./src/utils/*.js", "#jobs/*": "./src/jobs/*.js", "#db/*": "./src/db/*.js", "#models/*": "./src/db/models/*.js", "#middlewares/*": "./src/_middlewares/*.js"}}